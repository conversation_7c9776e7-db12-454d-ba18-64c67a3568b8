//
//  udp.m
//  udp_hook
//
//  Created by 小七 on 2025/6/29.
//

#import "udp.h"
#import "CaptainHook.h"
#import "fishhook.h"
#import <sys/socket.h>
#import <netinet/in.h>
#import <arpa/inet.h>
#import <dlfcn.h>
#import <CommonCrypto/CommonCryptor.h>
@implementation udp

// 保存原始函数指针
static ssize_t (*original_sendto)(int socket, const void *buffer, size_t length, int flags, const struct sockaddr *dest_addr, socklen_t dest_len);

// hook后的sendto函数
static ssize_t hooked_sendto(int socket, const void *buffer, size_t length, int flags, const struct sockaddr *dest_addr, socklen_t dest_len) {

    struct sockaddr_storage modified_addr;
    const struct sockaddr *final_addr = dest_addr;
    socklen_t final_len = dest_len;
    
    if (dest_addr && dest_addr->sa_family == AF_INET) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)dest_addr;
        char ip_str[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &(addr_in->sin_addr), ip_str, INET_ADDRSTRLEN);
        
        if (strcmp(ip_str, "***************") == 0) {
            struct sockaddr_in *new_addr = (struct sockaddr_in *)&modified_addr;
            memcpy(new_addr, addr_in, sizeof(struct sockaddr_in));
            inet_pton(AF_INET, "**************", &(new_addr->sin_addr));

            final_addr = (struct sockaddr *)new_addr;
            final_len = sizeof(struct sockaddr_in);
        }
        //        if (strcmp(ip_str, "*************") == 0) {
        //            struct sockaddr_in *new_addr = (struct sockaddr_in *)&modified_addr;
        //            memcpy(new_addr, addr_in, sizeof(struct sockaddr_in));
        //            inet_pton(AF_INET, "**************", &(new_addr->sin_addr));
        //
        //            final_addr = (struct sockaddr *)new_addr;
        //            final_len = sizeof(struct sockaddr_in);
        //        }
        
        
//
//        if (ntohs(addr_in->sin_port) == 3817 && buffer && length > 0) {
//            NSString *dataString = [[NSString alloc] initWithBytes:buffer length:length encoding:NSUTF8StringEncoding];
//            NSArray *components = [dataString componentsSeparatedByString:@"&"];
//            NSString *serialNumber = components[0];
//            NSString *replacedString = [dataString stringByReplacingOccurrencesOfString:serialNumber withString:wx_zyyz()];
//            NSData *modifiedData = [replacedString dataUsingEncoding:NSUTF8StringEncoding];
//            if (modifiedData) {
//                ssize_t result = original_sendto(socket, modifiedData.bytes, modifiedData.length, flags, final_addr, final_len);
//                return result;
//            }
//        }
    }
    ssize_t result = original_sendto(socket, buffer, length, flags, final_addr, final_len);
    
    return result;
}

NSString * wx_zyyz(void){
    NSString *serialNumber = nil;
    void *IOKit = dlopen("/System/Library/Frameworks/IOKit.framework/IOKit", RTLD_NOW);
    if (IOKit)
    {
        mach_port_t *kIOMasterPortDefault = dlsym(IOKit, "kIOMasterPortDefault");
        CFMutableDictionaryRef (*IOServiceMatching)(const char *name) = dlsym(IOKit, "IOServiceMatching");
        mach_port_t (*IOServiceGetMatchingService)(mach_port_t masterPort, CFDictionaryRef matching) = dlsym(IOKit, "IOServiceGetMatchingService");
        CFTypeRef (*IORegistryEntryCreateCFProperty)(mach_port_t entry, CFStringRef key, CFAllocatorRef allocator, uint32_t options) = dlsym(IOKit, "IORegistryEntryCreateCFProperty");
        kern_return_t (*IOObjectRelease)(mach_port_t object) = dlsym(IOKit, "IOObjectRelease");
        
        if (kIOMasterPortDefault && IOServiceGetMatchingService && IORegistryEntryCreateCFProperty && IOObjectRelease)
        {
            mach_port_t platformExpertDevice = IOServiceGetMatchingService(*kIOMasterPortDefault, IOServiceMatching("IOPlatformExpertDevice"));
            if (platformExpertDevice)
            {
                CFTypeRef platformSerialNumber = IORegistryEntryCreateCFProperty(platformExpertDevice, CFSTR("IOPlatformSerialNumber"), kCFAllocatorDefault, 0);
                if (CFGetTypeID(platformSerialNumber) == CFStringGetTypeID())
                {
                    serialNumber = [NSString stringWithString:(__bridge NSString*)platformSerialNumber];
                    CFRelease(platformSerialNumber);
                }
                IOObjectRelease(platformExpertDevice);
            }
        }
        dlclose(IOKit);
    }
    return serialNumber;

}

// 自定义的 CCCrypt 函数实现
static CCCryptorStatus custom_CCCrypt(
    CCOperation op,             /* kCCEncrypt, etc. */
    CCAlgorithm alg,            /* kCCAlgorithmAES128, etc. */
    CCOptions options,          /* kCCOptionPKCS7Padding, etc. */
    const void *key,            /* raw key material */
    size_t keyLength,           /* length of key material */
    const void *iv,             /* optional initialization vector */
    const void *dataIn,         /* optional per op and alg */
    size_t dataInLength,        /* length of data */
    void *dataOut,              /* data RETURNED here */
    size_t dataOutAvailable,    /* max length of dataOut */
    size_t *dataOutMoved)       /* number of bytes written */
{
    // 立即打印，确保函数被调用
    printf("[HOOK] !!!!! 自定义CCCrypt函数被调用了 !!!!!\n");
    fflush(stdout);
    NSLog(@"[HOOK] ===== 自定义CCCrypt函数被调用了！=====");
    NSLog(@"[HOOK] 函数地址: %p", custom_CCCrypt);
    NSLog(@"[HOOK] CCCrypt 参数 - op: %d, alg: %d, options: %d, keyLength: %zu, dataInLength: %zu",
          op, alg, options, keyLength, dataInLength);

    // 先获取原始函数，避免在打印过程中出现问题
    static CCCryptorStatus (*original_CCCrypt)(CCOperation, CCAlgorithm, CCOptions, const void *, size_t, const void *, const void *, size_t, void *, size_t, size_t *) = NULL;

    if (!original_CCCrypt) {
        NSLog(@"[HOOK] 正在获取原始CCCrypt函数...");
        void *handle = dlopen("/usr/lib/system/libcommonCrypto.dylib", RTLD_NOW);
        if (handle) {
            original_CCCrypt = dlsym(handle, "CCCrypt");
            NSLog(@"[HOOK] 原始CCCrypt函数地址: %p", original_CCCrypt);
        }
        if (!original_CCCrypt) {
            NSLog(@"[HOOK] 无法获取原始 CCCrypt 函数，尝试系统调用");
            return kCCParamError;
        }
    }

    // 打印 key 信息
    if (key && keyLength > 0) {
        // 尝试作为字符串打印
        NSString *keyString = [[NSString alloc] initWithBytes:key length:keyLength encoding:NSUTF8StringEncoding];
        if (keyString) {
            NSLog(@"[HOOK] Key (字符串): %@", keyString);
        } else {
            NSLog(@"[HOOK] Key 无法转换为UTF-8字符串");
        }

        // 打印16进制格式
        NSMutableString *keyHex = [NSMutableString string];
        const unsigned char *keyBytes = (const unsigned char *)key;
        for (size_t i = 0; i < keyLength; i++) {
            [keyHex appendFormat:@"%02X", keyBytes[i]];
        }
        NSLog(@"[HOOK] Key (16进制): %@", keyHex);
    } else {
        NSLog(@"[HOOK] Key: NULL 或长度为0");
    }

    // 打印 iv 信息
    if (iv) {
        // 根据算法确定IV长度 (AES通常是16字节)
        size_t ivLength = 16; // 默认AES块大小
        if (alg == kCCAlgorithmDES) {
            ivLength = 8;
        } else if (alg == kCCAlgorithm3DES) {
            ivLength = 8;
        }

        // 尝试作为字符串打印
        NSString *ivString = [[NSString alloc] initWithBytes:iv length:ivLength encoding:NSUTF8StringEncoding];
        if (ivString) {
            NSLog(@"[HOOK] IV (字符串): %@", ivString);
        } else {
            NSLog(@"[HOOK] IV 无法转换为UTF-8字符串");
        }

        // 打印16进制格式
        NSMutableString *ivHex = [NSMutableString string];
        const unsigned char *ivBytes = (const unsigned char *)iv;
        for (size_t i = 0; i < ivLength; i++) {
            [ivHex appendFormat:@"%02X", ivBytes[i]];
        }
        NSLog(@"[HOOK] IV (16进制): %@", ivHex);
    } else {
        NSLog(@"[HOOK] IV: NULL");
    }

    // 获取原始 CCCrypt 函数并调用
    static CCCryptorStatus (*original_CCCrypt)(CCOperation, CCAlgorithm, CCOptions, const void *, size_t, const void *, const void *, size_t, void *, size_t, size_t *) = NULL;

    if (!original_CCCrypt) {
        // 获取系统原始的 CCCrypt 函数
        void *handle = dlopen("/usr/lib/system/libcommonCrypto.dylib", RTLD_NOW);
        if (handle) {
            original_CCCrypt = dlsym(handle, "CCCrypt");
        }
        if (!original_CCCrypt) {
            NSLog(@"[HOOK] 无法获取原始 CCCrypt 函数");
            return kCCParamError;
        }
    }

    // 调用原始函数
    CCCryptorStatus result = original_CCCrypt(op, alg, options, key, keyLength, iv, dataIn, dataInLength, dataOut, dataOutAvailable, dataOutMoved);

    NSLog(@"[HOOK] CCCrypt 执行完成，返回状态: %d, 输出数据长度: %zu", result, dataOutMoved ? *dataOutMoved : 0);

    return result;
}


CHDeclareClass(UILabel);
CHMethod1(void, UILabel, setText, NSString*, title)

{

    if([title containsString:@"火柴皮"]){
        title = @"AMG";

    }

    return CHSuper1(UILabel, setText,title);

    
}
// 定义原始dlsym函数指针
static void *(*orig_dlsym)(void *handle, const char *symbol);


// 替换的dlsym函数实现
static void *replaced_dlsym(void *handle, const char *symbol) {

//    if (strcmp(symbol, "objc_getClass") == 0) {
//        return orig_dlsym(handle, symbol);
//    }
    NSLog(@"[HOOK] dlsym 调用，符号名称: %s", symbol);

    // 检测 CCCrypt 符号调用
    if (strcmp(symbol, "CCCrypt") == 0) {
        NSLog(@"[HOOK] 检测到CCCrypt调用，返回自定义实现");

        // 打印调用堆栈
        NSArray *callStack = [NSThread callStackSymbols];
        NSLog(@"[HOOK] CCCrypt调用堆栈:");
        for (int i = 0; i < callStack.count && i < 10; i++) {
            NSLog(@"[HOOK] %d: %@", i, callStack[i]);
        }

        NSLog(@"[HOOK] 返回自定义CCCrypt函数地址: %p", (void *)custom_CCCrypt);

        // 测试：主动调用一次自定义函数验证是否工作
        NSLog(@"[HOOK] 测试调用自定义CCCrypt函数...");
        char testOutput[32];
        size_t testMoved = 0;
        CCCryptorStatus testResult = custom_CCCrypt(
            kCCEncrypt, kCCAlgorithmAES128, kCCOptionPKCS7Padding,
            "1234567890123456", 16, NULL,
            "test", 4, testOutput, sizeof(testOutput), &testMoved
        );
        NSLog(@"[HOOK] 测试调用完成，结果: %d", testResult);

        return (void *)custom_CCCrypt;
    }

    // 对于其他符号，只在是加密相关时打印堆栈
    if (strstr(symbol, "Crypt") || strstr(symbol, "crypt") ||
        strstr(symbol, "AES") || strstr(symbol, "DES") ||
        strstr(symbol, "Hash") || strstr(symbol, "hash") ||
        strstr(symbol, "MD5") || strstr(symbol, "SHA")) {

        // 打印调用堆栈
        NSArray *callStack = [NSThread callStackSymbols];
        NSLog(@"[HOOK] 加密相关符号调用堆栈:");
        for (int i = 0; i < callStack.count && i < 5; i++) {
            NSLog(@"[HOOK] %d: %@", i, callStack[i]);
        }
    }
//
//    if (strcmp(symbol, "MGCopyAnswer") == 0) {
//        NSLog(@"[HOOK] 检测到MGCopyAnswer调用，返回自定义实现");
//        return (void *)custom_MGCopyAnswer;
//    }

    
    void *result = orig_dlsym(handle, symbol);

    return result;
}

static void __attribute__((constructor)) ooo() {
    CHLoadLateClass(UILabel);
    CHHook1(UILabel, setText);
    struct rebinding sendto_rebinding = {"sendto", hooked_sendto, (void *)&original_sendto};
    rebind_symbols(&sendto_rebinding, 1);
    [udp xxx];
}

+(void)xxx{
    struct rebinding dlsym_rebinding = {"dlsym", (void *)replaced_dlsym, (void **)&orig_dlsym};
    struct rebinding rebindings[] = {dlsym_rebinding};
    rebind_symbols(rebindings, 1);
}

@end
